# Excel Template Integration

## <PERSON><PERSON><PERSON> thay đổi đã thực hiện:

### 1. **File Template Setup**
```
src/
├── asset/
│   └── template/
│       └── template.xlsx (original)
└── assets/
    └── template.xlsx (copied for webpack)
```

### 2. **Webpack Configuration**
```javascript
// webpack/webpack.base.babel.js
{
  test: /\.(xlsx|xls|csv)$/i,
  type: "asset/resource",
}
```

**Mục đích**: Cho phép webpack import và bundle file Excel như static assets.

### 3. **Download Function Update**
```javascript
const handleDownloadTemplate = async () => {
  try {
    // Import template file từ assets
    const templateFile = require('../../assets/template.xlsx');
    
    // Fetch file as blob
    const response = await fetch(templateFile);
    if (!response.ok) {
      throw new Error('Failed to fetch template file');
    }
    
    const blob = await response.blob();
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'marketing_users_template.xlsx';
    link.click();
    window.URL.revokeObjectURL(url);
    
    toast.success(t("TEMPLATE_DOWNLOADED_SUCCESS"));
  } catch (error) {
    console.error('Error downloading template:', error);
    // Fallback to generated template
    handleDownloadTemplateFallback();
  }
};
```

### 4. **Fallback Function**
```javascript
const handleDownloadTemplateFallback = () => {
  try {
    // Create CSV template if Excel file not available
    const templateData = [
      ['email', 'name', 'phone'],
      ['<EMAIL>', 'John Doe', '+1234567890'],
      ['<EMAIL>', 'Jane Smith', '+0987654321'],
      ['<EMAIL>', 'User Name', '+1122334455']
    ];

    const BOM = '\uFEFF';
    const csvContent = BOM + templateData.map(row => 
      row.map(cell => {
        if (cell.includes(',') || cell.includes('"') || cell.includes('\n')) {
          return `"${cell.replace(/"/g, '""')}"`;
        }
        return cell;
      }).join(',')
    ).join('\n');
    
    const blob = new Blob([csvContent], { 
      type: 'text/csv;charset=utf-8;' 
    });
    
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'marketing_users_template.csv';
    link.click();
    window.URL.revokeObjectURL(url);
    
    toast.success(t("TEMPLATE_DOWNLOADED_SUCCESS"));
  } catch (error) {
    console.error('Error in fallback template creation:', error);
    toast.error(t("TEMPLATE_DOWNLOAD_ERROR"));
  }
};
```

## Workflow:

### 1. **Primary Path**: Excel Template
- Import file `template.xlsx` từ assets
- Fetch file as blob
- Download với tên `marketing_users_template.xlsx`
- Giữ nguyên format và styling của file gốc

### 2. **Fallback Path**: Generated CSV
- Nếu Excel file không load được
- Tạo CSV template với sample data
- Download với tên `marketing_users_template.csv`
- Đảm bảo luôn có file template

## Benefits:

### 1. **Real Excel File**
- Sử dụng file Excel thực sự từ designer/admin
- Giữ nguyên formatting, styling, formulas
- Professional appearance
- Backend compatibility guaranteed

### 2. **Reliability**
- Fallback mechanism nếu file không load
- Error handling comprehensive
- User experience không bị gián đoạn

### 3. **Maintainability**
- File template có thể update độc lập
- Không cần thay đổi code khi update template
- Version control cho template files

### 4. **Performance**
- File được bundle với webpack
- Cached by browser
- Fast download experience

## File Structure Requirements:

### Template File Should Contain:
```
| Column A | Column B | Column C |
|----------|----------|----------|
| email    | name     | phone    |
| (sample) | (sample) | (sample) |
```

### Recommended Template Features:
- Header row với styling (bold, background color)
- Sample data rows
- Data validation rules (optional)
- Instructions sheet (optional)
- Proper column widths

## Testing Checklist:

### Development:
- [ ] Webpack builds successfully
- [ ] Template file imports correctly
- [ ] Download function works
- [ ] Fallback triggers when needed

### Production:
- [ ] File bundled correctly
- [ ] Download works in all browsers
- [ ] File opens properly in Excel
- [ ] Backend can parse the template format

### Error Scenarios:
- [ ] File not found → Fallback works
- [ ] Network error → Error message shown
- [ ] Invalid file → Graceful handling

## Future Enhancements:

1. **Dynamic Templates**: Generate templates based on user settings
2. **Multiple Languages**: Different templates for different locales
3. **Advanced Features**: Formulas, charts, multiple sheets
4. **Version Management**: Template versioning system
5. **User Upload**: Allow admins to upload custom templates

## Notes:

- File `template.xlsx` phải có format đúng để backend parse được
- Webpack rule mới cho phép import Excel files
- Fallback đảm bảo system luôn hoạt động
- Error handling comprehensive cho production use

Giải pháp này kết hợp tính linh hoạt của file template thực sự với độ tin cậy của fallback mechanism.
