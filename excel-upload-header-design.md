# Excel Upload Header Design

## Thiết kế mới đã thực hiện:

### 1. **Card Header Layout**
```
┌─────────────────────────────────────────────────────────────┐
│ [📊 Upload Excel File]              [📥 Download Template] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Upload Area Content...                                      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2. **Header Components**
- **Left side**: Icon + Title "Upload Excel File"
- **Right side**: Download Template button (primary green)
- **Responsive**: Stack vertically on mobile

### 3. **Button Styling**
- **Color**: Green (#52c41a) thay vì blue
- **Style**: Primary button với shadow
- **Hover effects**: Lift up + darker green
- **Icon**: Download icon rõ ràng
- **Size**: Medium (36px height)

### 4. **Typography**
- **Title**: Font weight 600, size 16px
- **Button text**: Font weight 500
- **Color**: Dark (#333) cho light theme, white cho dark theme

### 5. **Spacing & Layout**
- **Justify**: Space between title và button
- **Alignment**: Center aligned vertically
- **Gap**: 12px cho mobile stack layout

### 6. **Interactive Effects**
- **Hover**: Transform translateY(-1px)
- **Shadow**: Tăng shadow khi hover
- **Transition**: Smooth 0.3s ease
- **Active**: Reset transform khi click

### 7. **Responsive Behavior**
```css
Desktop: [Title ←→ Button]
Mobile:  [Title]
         [Button (full width)]
```

### 8. **Dark Theme Support**
- **Background**: #1f1f1f
- **Title color**: White
- **Button**: Darker green (#389e0d)
- **Hover**: Lighter green (#52c41a)

## CSS Classes Structure:

```scss
.excel-upload-card {
  .excel-upload-header {
    // Flex layout space-between
    
    .header-title {
      // Icon + text layout
    }
    
    .download-template-btn {
      // Green primary button
      // Hover effects
      // Shadow styling
    }
  }
}
```

## Ưu điểm của thiết kế mới:

1. **Compact**: Tiết kiệm không gian bằng cách đặt trong header
2. **Clear hierarchy**: Title và action button rõ ràng
3. **Accessible**: Button size và contrast tốt
4. **Responsive**: Hoạt động tốt trên mobile
5. **Consistent**: Theo design system với green theme
6. **Interactive**: Hover effects mượt mà

## So sánh với thiết kế cũ:

**Trước:**
- Template download ở card riêng
- Chiếm nhiều không gian
- Layout phức tạp

**Sau:**
- Tích hợp vào header
- Gọn gàng, rõ ràng
- Button nổi bật với green color
- Responsive tốt hơn

Thiết kế mới này tạo cảm giác chuyên nghiệp và dễ sử dụng hơn, đồng thời tiết kiệm không gian hiển thị.
