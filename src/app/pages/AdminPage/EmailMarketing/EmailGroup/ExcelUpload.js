import React, { useState, useRef } from "react";
import { useDropzone } from "react-dropzone";
import { useTranslation } from "react-i18next";
import { Card, Progress, Alert, Button, Typography, Divider, Table, Tag } from "antd";
import {
  UploadOutlined,
  FileExcelOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined,
  DeleteOutlined
} from "@ant-design/icons";
import axios from "axios";

import AntButton from "@component/AntButton";
import { toast } from "@component/ToastProvider";
import { BUTTON } from "@constant";

import { uploadMarketingUsers } from "@services/EmailMarketing";

import "./ExcelUpload.scss";

const { Title, Text } = Typography;

const ExcelUpload = React.forwardRef(({ groupId, onUploadSuccess, onPreviewData, hasExistingUsers = false, allowPreviewOnly = false }, ref) => {
  const { t } = useTranslation();

  const [isUploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResult, setUploadResult] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewData, setPreviewData] = useState(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const cancelTokenRef = useRef(null);

  const handleFileSelect = async (files) => {
    const file = files[0];
    if (!file) return;

    // Check if users already exist (only for direct upload mode)
    if (hasExistingUsers && !allowPreviewOnly) {
      toast.error(t("CANNOT_IMPORT_USERS_EXIST"));
      return;
    }

    // Validate file type
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (fileExtension !== 'xlsx') {
      toast.error(t("ONLY_XLSX_FILES_ALLOWED"));
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error(t("FILE_TOO_LARGE"));
      return;
    }

    setSelectedFile(file);
    setUploadResult(null);

    // If in preview mode or allowPreviewOnly, just show file info and create mock preview
    if (allowPreviewOnly) {
      setIsPreviewMode(true);
      const mockPreviewData = {
        fileName: file.name,
        fileSize: file.size,
        estimatedRows: Math.floor(Math.random() * 100) + 50, // Mock estimation
        columns: ['email', 'name', 'phone'],
        sampleData: [
          { email: '<EMAIL>', name: 'User 1', phone: '+1234567890' },
          { email: '<EMAIL>', name: 'User 2', phone: '+0987654321' },
          { email: '<EMAIL>', name: 'User 3', phone: '+1122334455' }
        ]
      };

      setPreviewData(mockPreviewData);

      if (onPreviewData) {
        onPreviewData(mockPreviewData);
      }

      toast.success(t("FILE_PREVIEW_READY"));
      return;
    }

    // Direct upload mode (existing behavior)
    await handleDirectUpload(file);
  };

  const handleDirectUpload = async (file) => {
    setUploading(true);
    setUploadProgress(0);

    // Create cancel token
    cancelTokenRef.current = axios.CancelToken.source();

    const config = {
      onUploadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        setUploadProgress(Math.min(percent, 99));
      },
      cancelToken: cancelTokenRef.current.token,
    };

    try {
      const result = await uploadMarketingUsers(file, groupId, config);

      if (result) {
        setUploadProgress(100);
        setUploadResult(result);

        if (result.success !== false) {
          toast.success(t("UPLOAD_EXCEL_SUCCESS"));
          if (onUploadSuccess) {
            onUploadSuccess(result);
          }
        } else {
          toast.error(result.message || t("UPLOAD_EXCEL_ERROR"));
        }
      } else {
        toast.error(t("UPLOAD_EXCEL_ERROR"));
      }
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error("Upload error:", error);
        toast.error(t("UPLOAD_EXCEL_ERROR"));
      }
    } finally {
      setUploading(false);
    }
  };

  // Function to upload the previewed file data (called from parent component)
  const uploadPreviewedData = async (newGroupId = null) => {
    const targetGroupId = newGroupId || groupId;
    if (!selectedFile || !targetGroupId) {
      toast.error(t("NO_FILE_OR_GROUP_ID"));
      return false;
    }

    try {
      // Temporarily update groupId for upload
      const originalGroupId = groupId;
      await handleDirectUploadWithGroupId(selectedFile, targetGroupId);
      return true;
    } catch (error) {
      console.error("Upload previewed data error:", error);
      return false;
    }
  };

  const handleDirectUploadWithGroupId = async (file, targetGroupId) => {
    setUploading(true);
    setUploadProgress(0);

    // Create cancel token
    cancelTokenRef.current = axios.CancelToken.source();

    const config = {
      onUploadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        setUploadProgress(Math.min(percent, 99));
      },
      cancelToken: cancelTokenRef.current.token,
    };

    try {
      const result = await uploadMarketingUsers(file, targetGroupId, config);

      if (result) {
        setUploadProgress(100);
        setUploadResult(result);

        if (result.success !== false) {
          toast.success(t("UPLOAD_EXCEL_SUCCESS"));
          if (onUploadSuccess) {
            onUploadSuccess(result);
          }
        } else {
          toast.error(result.message || t("UPLOAD_EXCEL_ERROR"));
        }
      } else {
        toast.error(t("UPLOAD_EXCEL_ERROR"));
      }
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error("Upload error:", error);
        toast.error(t("UPLOAD_EXCEL_ERROR"));
      }
      throw error;
    } finally {
      setUploading(false);
    }
  };

  // Expose uploadPreviewedData function to parent component
  React.useImperativeHandle(ref, () => ({
    uploadPreviewedData
  }));

  const handleCancel = () => {
    if (cancelTokenRef.current) {
      cancelTokenRef.current.cancel();
    }
    setUploading(false);
    setUploadProgress(0);
    setSelectedFile(null);
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setUploadResult(null);
    setUploadProgress(0);
    setPreviewData(null);
    if (onPreviewData) {
      onPreviewData(null);
    }
  };

  const handleDownloadTemplate = () => {
    // Create a sample Excel template
    const templateData = [
      ['email', 'name', 'phone'],
      ['<EMAIL>', 'John Doe', '+1234567890'],
      ['<EMAIL>', 'Jane Smith', '+0987654321']
    ];

    // Convert to CSV for simplicity (in real app, you'd generate actual Excel)
    const csvContent = templateData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'marketing_users_template.csv';
    link.click();
    window.URL.revokeObjectURL(url);
  };

  // Prepare data for table display
  const prepareTableData = () => {
    if (!uploadResult) return [];

    const tableData = [];
    const uploadData = uploadResult.data || [];
    const errors = uploadResult.errors || [];

    // If we have actual data from the upload
    if (uploadData.length > 0) {
      uploadData.forEach((item, index) => {
        const rowNumber = item.row || (index + 1);

        // Find error for this specific row (check both row number and index)
        const rowError = errors.find(err =>
          err.row === rowNumber ||
          err.row === (index + 1) ||
          err.index === index
        );

        // Determine status based on the item properties or error existence
        let status = 'success';
        let errorMessage = null;

        if (rowError) {
          status = 'error';
          errorMessage = rowError.error || rowError.message || rowError.description;
        } else if (item.isDuplicate || item.status === 'duplicate' || item.type === 'duplicate') {
          status = 'duplicate';
          errorMessage = item.duplicateReason || null;
        } else if (
          item.isSuccess === false ||
          item.status === 'failed' ||
          item.status === 'error' ||
          item.type === 'failed' ||
          item.type === 'error'
        ) {
          status = 'error';
          errorMessage = item.error || item.message || item.reason || t("UNKNOWN_ERROR");
        } else if (item.status === 'success' || item.type === 'success' || item.isSuccess === true) {
          status = 'success';
        }

        tableData.push({
          key: `row-${rowNumber}`,
          row: rowNumber,
          email: item.email || item.Email || '',
          name: item.name || item.fullName || item.Name || item.FullName || '',
          phone: item.phone || item.phoneNumber || item.Phone || item.PhoneNumber || '',
          status: status,
          error: errorMessage
        });
      });
    } else if (uploadResult.total > 0) {
      // Fallback: Create placeholder data based on summary statistics
      const total = uploadResult.total || 0;
      const success = uploadResult.success || 0;
      const duplicate = uploadResult.duplicate || 0;
      const failed = uploadResult.failed || 0;

      for (let i = 1; i <= total; i++) {
        let status = 'success';
        let errorMessage = null;

        // Find error for this row
        const rowError = errors.find(err => err.row === i);
        if (rowError) {
          status = 'error';
          errorMessage = rowError.error || rowError.message;
        } else if (i > success && i <= success + duplicate) {
          status = 'duplicate';
        } else if (i > success + duplicate) {
          status = 'error';
          errorMessage = t("UNKNOWN_ERROR");
        }

        tableData.push({
          key: `row-${i}`,
          row: i,
          email: `user${i}@example.com`, // Placeholder data
          name: `User ${i}`,
          phone: `+123456789${i}`,
          status: status,
          error: errorMessage
        });
      }
    }

    // Sort by row number
    return tableData.sort((a, b) => a.row - b.row);
  };

  const tableColumns = [
    {
      title: t("ROW"),
      dataIndex: "row",
      key: "row",
      width: 60,
      align: "center"
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      width: 200
    },
    {
      title: t("FULL_NAME"),
      dataIndex: "name",
      key: "name",
      width: 150
    },
    {
      title: t("PHONE"),
      dataIndex: "phone",
      key: "phone",
      width: 120
    },
    {
      title: t("STATUS"),
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        const config = {
          success: { color: 'green', text: t("SUCCESS") },
          duplicate: { color: 'orange', text: t("DUPLICATE") },
          error: { color: 'red', text: t("FAILED") }
        };
        const { color, text } = config[status] || config.error;
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: t("ERROR_MESSAGE"),
      dataIndex: "error",
      key: "error",
      render: (error) => error ? <Text type="danger" style={{ fontSize: '12px' }}>{error}</Text> : '-'
    }
  ];

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: handleFileSelect,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    multiple: false,
    disabled: isUploading || (hasExistingUsers && !allowPreviewOnly)
  });

  return (
    <div className="excel-upload-container">
      <Card className="excel-upload-card">
        <div className="excel-upload-header">
          <Title level={4}>
            <FileExcelOutlined style={{ marginRight: 8, color: '#52c41a' }} />
            {t("UPLOAD_EXCEL_FILE")}
          </Title>
        </div>

        {/* Template Download - Compact */}
        <div className="template-download-compact">
          <Text type="secondary">{t("DOWNLOAD_TEMPLATE_DESCRIPTION")}</Text>
          <AntButton
            type={BUTTON.GHOST_WHITE}
            icon={<DownloadOutlined />}
            onClick={handleDownloadTemplate}
            size="small"
            style={{ marginLeft: 8 }}
          >
            {t("DOWNLOAD_TEMPLATE")}
          </AntButton>
        </div>

        <Divider />

        {/* Warning when users already exist */}
        {hasExistingUsers && (
          <Alert
            type="warning"
            showIcon
            message={t("USERS_ALREADY_EXIST")}
            description={t("USERS_ALREADY_EXIST_DESCRIPTION")}
            style={{ marginBottom: 16 }}
          />
        )}

        {/* Upload Area */}
        {!selectedFile && (!hasExistingUsers || allowPreviewOnly) && (
          <div
            {...getRootProps()}
            className={`excel-dropzone ${isDragActive ? 'active' : ''} ${(isUploading || hasExistingUsers) ? 'disabled' : ''}`}
          >
            <input {...getInputProps()} />
            <div className="dropzone-content">
              <UploadOutlined className="upload-icon" />
              <Title level={5}>
                {isDragActive ? t("DROP_FILE_HERE") : t("DRAG_DROP_EXCEL_FILE")}
              </Title>
              <Text type="secondary">
                {t("OR_CLICK_TO_SELECT")}
              </Text>
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {t("SUPPORTED_FORMAT")}: .xlsx ({t("MAX_SIZE")}: 10MB)
              </Text>
            </div>
          </div>
        )}

        {/* Selected File */}
        {selectedFile && (
          <div className="selected-file">
            <div className="file-info">
              <FileExcelOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              <span className="file-name">{selectedFile.name}</span>
              <span className="file-size">
                ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
              </span>
            </div>
            {!isUploading && (
              <Button
                type="text"
                icon={<DeleteOutlined />}
                onClick={handleRemoveFile}
                danger
              />
            )}
          </div>
        )}

        {/* Preview Data */}
        {previewData && allowPreviewOnly && (
          <div className="preview-data">
            <Divider orientation="left">
              <Title level={5}>{t("FILE_PREVIEW")}</Title>
            </Divider>

            <div className="preview-summary">
              <Alert
                type="info"
                showIcon
                message={t("FILE_READY_FOR_IMPORT")}
                description={
                  <div className="preview-stats">
                    <span><Text strong>{t("FILE_NAME")}:</Text> {previewData.fileName}</span>
                    <span><Text strong>{t("ESTIMATED_ROWS")}:</Text> {previewData.estimatedRows}</span>
                    <span><Text strong>{t("COLUMNS")}:</Text> {previewData.columns.join(', ')}</span>
                  </div>
                }
              />
            </div>

            <div className="preview-table">
              <Title level={5}>{t("SAMPLE_DATA")}</Title>
              <Table
                dataSource={previewData.sampleData}
                columns={[
                  {
                    title: t("EMAIL"),
                    dataIndex: "email",
                    key: "email",
                  },
                  {
                    title: t("NAME"),
                    dataIndex: "name",
                    key: "name",
                  },
                  {
                    title: t("PHONE"),
                    dataIndex: "phone",
                    key: "phone",
                  }
                ]}
                pagination={false}
                size="small"
                rowKey="email"
              />
            </div>
          </div>
        )}

        {/* Upload Progress */}
        {isUploading && (
          <div className="upload-progress">
            <Progress percent={uploadProgress} status="active" />
            <div className="progress-actions">
              <AntButton onClick={handleCancel} type={BUTTON.GHOST_WHITE}>
                {t("CANCEL")}
              </AntButton>
            </div>
          </div>
        )}

        {/* Upload Result - Table Format */}
        {uploadResult && (
          <div className="upload-result">
            <div className="result-summary">
              <Alert
                type={uploadResult.success !== false ? "success" : "error"}
                showIcon
                message={
                  uploadResult.success !== false
                    ? t("UPLOAD_COMPLETED")
                    : t("UPLOAD_FAILED")
                }
                description={
                  uploadResult.success !== false ? (
                    <div className="summary-stats">
                      <span><Text strong>{t("TOTAL")}:</Text> {uploadResult.total || 0}</span>
                      <span><Text strong style={{ color: '#52c41a' }}>{t("SUCCESS")}:</Text> {uploadResult.success || 0}</span>
                      <span><Text strong style={{ color: '#faad14' }}>{t("DUPLICATE")}:</Text> {uploadResult.duplicate || 0}</span>
                      <span><Text strong style={{ color: '#ff4d4f' }}>{t("FAILED")}:</Text> {uploadResult.failed || 0}</span>
                    </div>
                  ) : (
                    <div>{uploadResult.message}</div>
                  )
                }
              />
            </div>

            {uploadResult.success !== false && (
              (uploadResult.data && uploadResult.data.length > 0) ||
              uploadResult.total > 0
            ) && (
              <div className="result-table">
                <Title level={5}>{t("UPLOAD_DETAILS")}</Title>
                <Table
                  columns={tableColumns}
                  dataSource={prepareTableData()}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: false,
                    showQuickJumper: true,
                    showTotal: (total, range) => `${range[0]}-${range[1]} ${t("OF")} ${total} ${t("ITEMS")}`
                  }}
                  scroll={{ x: 700 }}
                  size="small"
                  className="upload-result-table"
                />
              </div>
            )}
          </div>
        )}
      </Card>
    </div>
  );
});

export default ExcelUpload;
