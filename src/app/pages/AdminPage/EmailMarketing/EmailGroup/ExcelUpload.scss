.excel-upload-container {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .template-download-card {
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;

    .template-download-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 0;

      .template-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .template-icon {
          font-size: 20px;
          color: #1890ff;
        }

        .template-text {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .template-description {
            font-size: 12px;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .excel-upload-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .excel-upload-header {
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
    }

    .upload-section {
      margin: 20px 0;

      .excel-dropzone {
        border: 2px dashed #d9d9d9;
        border-radius: 12px;
        padding: 32px 20px;
        text-align: center;
        background: #fafafa;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          background: #f0f8ff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }

        &.active {
          border-color: #52c41a;
          background: #f6ffed;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.6;
          background: #f5f5f5;
          border-color: #d9d9d9;

          &:hover {
            border-color: #d9d9d9;
            background: #f5f5f5;
            transform: none;
            box-shadow: none;
          }

          .upload-icon {
            color: #bfbfbf !important;
          }
        }

        .dropzone-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;

          .upload-icon-wrapper {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;

            .upload-icon {
              font-size: 28px;
              color: white;
            }
          }

          .upload-text {
            text-align: center;

            .upload-subtitle {
              font-size: 14px;
            }
          }

          .upload-info {
            .format-info {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }
    }

    .selected-file-section {
      margin: 20px 0;

      .selected-file-card {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 16px;
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 12px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
        }

        .file-icon-wrapper {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          background: #52c41a;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .file-icon {
            font-size: 24px;
            color: white;
          }
        }

        .file-details {
          flex: 1;
          min-width: 0;

          .file-name {
            font-weight: 600;
            color: #333;
            font-size: 14px;
            margin-bottom: 4px;
            word-break: break-all;
          }

          .file-meta {
            display: flex;
            gap: 12px;
            align-items: center;

            .file-size, .file-type {
              font-size: 12px;
              color: #8c8c8c;
            }

            .file-type {
              &::before {
                content: "•";
                margin-right: 4px;
                color: #d9d9d9;
              }
            }
          }
        }

        .file-actions {
          .remove-file-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              background: #fff2f0;
            }
          }
        }
      }
    }

    .upload-progress {
      margin: 16px 0;

      .ant-progress {
        margin-bottom: 16px;
      }

      .progress-actions {
        text-align: center;
      }
    }

    .preview-data-section {
      margin: 24px 0;

      .preview-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        .preview-icon {
          font-size: 18px;
          color: #52c41a;
        }
      }

      .preview-summary-card {
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;

        .preview-stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
          margin-bottom: 16px;

          .stat-item {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .stat-label {
              font-size: 12px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }

            .stat-value {
              font-size: 14px;
              word-break: break-all;
            }
          }
        }

        .preview-alert {
          margin: 0;
          border: none;
          background: rgba(82, 196, 26, 0.1);
        }
      }

      .preview-table-section {
        .table-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .table-subtitle {
            font-size: 12px;
          }
        }

        .preview-table {
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

          .ant-table-thead > tr > th {
            background: #fafafa;
            font-weight: 600;
            font-size: 12px;
            border-bottom: 1px solid #e8e8e8;
          }

          .ant-table-tbody > tr > td {
            font-size: 12px;
            border-bottom: 1px solid #f0f0f0;
          }

          .ant-table-tbody > tr:last-child > td {
            border-bottom: none;
          }
        }
      }
    }

    .upload-result {
      margin: 16px 0;

      .result-summary {
        margin-bottom: 16px;

        .summary-stats {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;

          > span {
            white-space: nowrap;
          }
        }
      }

      .result-table {
        .upload-result-table {
          .ant-table-thead > tr > th {
            background: #fafafa;
            font-weight: 600;
            font-size: 12px;
            padding: 8px 12px;
          }

          .ant-table-tbody > tr > td {
            padding: 8px 12px;
            font-size: 12px;
          }

          .ant-table-container {
            border: 1px solid #f0f0f0;
            border-radius: 6px;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .excel-upload-container {
    .template-download-card {
      .template-download-content {
        flex-direction: column;
        gap: 12px;
        text-align: center;

        .template-info {
          justify-content: center;
        }
      }
    }

    .excel-upload-card {
      .upload-section {
        .excel-dropzone {
          padding: 24px 15px;

          .dropzone-content {
            .upload-icon-wrapper {
              width: 56px;
              height: 56px;

              .upload-icon {
                font-size: 24px;
              }
            }
          }
        }
      }

      .selected-file-section {
        .selected-file-card {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          .file-details {
            width: 100%;
          }

          .file-actions {
            align-self: flex-end;
          }
        }
      }

      .preview-data-section {
        .preview-summary-card {
          .preview-stats-grid {
            grid-template-columns: 1fr;
            gap: 12px;
          }
        }
      }

      .upload-result {
        .result-summary {
          .summary-stats {
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }
  }
}

// Dark theme support
.dark-theme {
  .excel-upload-container {
    .template-download-card {
      background: #1f1f1f;
      border-color: #434343;

      .template-download-content {
        .template-info {
          .template-icon {
            color: #40a9ff;
          }
        }
      }
    }

    .excel-upload-card {
      background: #1f1f1f;
      border-color: #434343;

      .upload-section {
        .excel-dropzone {
          background: #2a2a2a;
          border-color: #434343;

          &:hover {
            background: #3a3a3a;
            border-color: #1890ff;
          }

          &.active {
            background: #2a3a2a;
            border-color: #52c41a;
          }
        }
      }

      .selected-file-section {
        .selected-file-card {
          background: #2a3a2a;
          border-color: #4a6a4a;
        }
      }

      .preview-data-section {
        .preview-summary-card {
          background: #2a3a2a;
          border-color: #4a6a4a;
        }

        .preview-table-section {
          .preview-table {
            .ant-table-thead > tr > th {
              background: #2a2a2a;
              border-color: #434343;
            }
          }
        }
      }

      .upload-result {
        .result-table {
          .upload-result-table {
            .ant-table-thead > tr > th {
              background: #2a2a2a;
              border-color: #434343;
            }

            .ant-table-container {
              border-color: #434343;
            }
          }
        }
      }
    }
  }
}
