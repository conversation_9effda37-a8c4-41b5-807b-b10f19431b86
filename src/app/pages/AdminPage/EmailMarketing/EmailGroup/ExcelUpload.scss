.excel-upload-container {
  margin-top: 16px;

  .excel-upload-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .excel-upload-header {
      text-align: center;
      margin-bottom: 0;

      .ant-typography {
        margin-bottom: 8px;
      }
    }

    .template-download-compact {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;
      margin-bottom: 16px;

      .ant-btn {
        flex-shrink: 0;
      }
    }

    .excel-dropzone {
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      padding: 40px 20px;
      text-align: center;
      background: #fafafa;
      cursor: pointer;
      transition: all 0.3s ease;
      margin: 16px 0;

      &:hover {
        border-color: #1890ff;
        background: #f0f8ff;
      }

      &.active {
        border-color: #52c41a;
        background: #f6ffed;
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.6;
        background: #f5f5f5;
        border-color: #d9d9d9;

        &:hover {
          border-color: #d9d9d9;
          background: #f5f5f5;
        }

        .upload-icon {
          color: #bfbfbf !important;
        }
      }

      .dropzone-content {
        .upload-icon {
          font-size: 48px;
          color: #1890ff;
          margin-bottom: 16px;
        }

        .ant-typography {
          margin-bottom: 8px;
        }
      }
    }

    .selected-file {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #f0f8ff;
      border: 1px solid #91d5ff;
      border-radius: 6px;
      margin: 16px 0;

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;

        .file-name {
          font-weight: 500;
          margin-right: 8px;
        }

        .file-size {
          color: #8c8c8c;
          font-size: 12px;
        }
      }
    }

    .upload-progress {
      margin: 16px 0;

      .ant-progress {
        margin-bottom: 16px;
      }

      .progress-actions {
        text-align: center;
      }
    }

    .preview-data {
      margin: 16px 0;

      .preview-summary {
        margin-bottom: 16px;

        .preview-stats {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;

          > span {
            white-space: nowrap;
          }
        }
      }

      .preview-table {
        .ant-table {
          font-size: 12px;
        }
      }
    }

    .upload-result {
      margin: 16px 0;

      .result-summary {
        margin-bottom: 16px;

        .summary-stats {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;

          > span {
            white-space: nowrap;
          }
        }
      }

      .result-table {
        .upload-result-table {
          .ant-table-thead > tr > th {
            background: #fafafa;
            font-weight: 600;
            font-size: 12px;
            padding: 8px 12px;
          }

          .ant-table-tbody > tr > td {
            padding: 8px 12px;
            font-size: 12px;
          }

          .ant-table-container {
            border: 1px solid #f0f0f0;
            border-radius: 6px;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .excel-upload-container {
    .excel-upload-card {
      .template-download-compact {
        flex-direction: column;
        gap: 8px;
        text-align: center;
      }

      .excel-dropzone {
        padding: 30px 15px;

        .dropzone-content {
          .upload-icon {
            font-size: 36px;
          }
        }
      }

      .selected-file {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .file-info {
          width: 100%;
        }
      }

      .upload-result {
        .result-summary {
          .summary-stats {
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }
  }
}

// Dark theme support
.dark-theme {
  .excel-upload-container {
    .excel-upload-card {
      background: #1f1f1f;
      border-color: #434343;

      .template-download-compact {
        background: #2a2a2a;
        border-color: #434343;
      }

      .excel-dropzone {
        background: #2a2a2a;
        border-color: #434343;

        &:hover {
          background: #3a3a3a;
          border-color: #1890ff;
        }

        &.active {
          background: #2a3a2a;
          border-color: #52c41a;
        }
      }

      .selected-file {
        background: #2a3a4a;
        border-color: #4a6a8a;
      }

      .upload-result {
        .result-table {
          .upload-result-table {
            .ant-table-thead > tr > th {
              background: #2a2a2a;
              border-color: #434343;
            }

            .ant-table-container {
              border-color: #434343;
            }
          }
        }
      }
    }
  }
}
