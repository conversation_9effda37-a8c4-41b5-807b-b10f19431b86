import React, {useEffect, useState, useRef} from "react";
import {useNavigate, useParams} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {Form, Input, Select, DatePicker, Row, Col, Radio, InputNumber, message, Collapse, Card, Alert} from "antd";
import {MinusCircleOutlined, PlusOutlined, SaveOutlined, UserOutlined, DownloadOutlined} from "@ant-design/icons";
import dayjs from "dayjs";
import {toast} from "@component/ToastProvider";
import Loading from "@component/Loading";
import {AntForm} from "@component/AntForm";
import AntButton from "@component/AntButton";
import TableAdmin from "@src/app/component/TableAdmin";
import RULE from "@rule";
import {LINK} from "@link";
import {BUTTON, CONSTANT} from "@constant";
import {orderColumn} from "@common/functionCommons";
import "../EmailMarketing.scss";
import "./EmailGroupDetail.scss";
import ExcelUpload from "./ExcelUpload";

import {
  getGroupDetail,
  createGroup,
  updateGroup,
  getUsersByAutomaticType,
  getUsersByConditions,
  getMarketingUsersByGroupId
} from "@services/EmailMarketing";
import {getAllPackage} from "@services/Package";
import {getAllPromotions} from "@services/Promotion";
import {t} from "i18next";

// Định nghĩa các loại nhóm
const GROUP_TYPES = [
  {value: "automatic", label: "Tự động"},
  {value: "manual", label: "Thủ công"},
  {value: "import", label: "Tải lên"}
];

// Định nghĩa các loại nhóm tự động
const AUTOMATIC_TYPES = [
  {value: "never_used_after_signup", label: "Người dùng đã đăng ký nhưng chưa bao giờ dùng sản phẩm"},
  {value: "inactive_over_14_days", label: "Người dùng ngưng sử dụng từ 14 ngày trở lên"},
  {value: "trial_expired_no_upgrade", label: "Người dùng hết hạn dùng thử mà chưa đăng ký trả phí"},
  {value: "paid_plan_expiring_soon", label: "Người dùng đang trong gói trả phí sắp hết hạn"},
  {value: "paid_plan_expired_7_days", label: "Người dùng đã hết hạn gói trả phí mà chưa gia hạn sau 7 ngày"},
  {value: "feedback_upgrade_intent", label: "Người dùng có feedback chọn nâng cấp"}
];

// Định nghĩa các trạng thái phản hồi
const RESPONSE_STATUS = [
  {value: "upgrade", label: "Nâng cấp"},
  {value: "not_upgrade", label: "Không nâng cấp"}
];

const EmailGroupDetail = () => {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const {id} = useParams();

  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [groupType, setGroupType] = useState("automatic");
  const [packageTypes, setPackageTypes] = useState([]);
  const [promotionCodes, setPromotionCodes] = useState([]);

  // User list states
  const [userListData, setUserListData] = useState([]);
  const [loadingUserList, setLoadingUserList] = useState(false);
  const [userListError, setUserListError] = useState(null);
  const [userListLoaded, setUserListLoaded] = useState(false);
  const [activeCollapseKeys, setActiveCollapseKeys] = useState([]);
  const [userListTotal, setUserListTotal] = useState(0);

  // Excel upload states
  const [excelPreviewData, setExcelPreviewData] = useState(null);
  const excelUploadRef = useRef(null);

  // Lấy dữ liệu nhóm khi id thay đổi
  useEffect(() => {
    loadInitialData();
  }, [id]);

  // Auto load user list when group type is import
  useEffect(() => {
    if (!isCreating && groupType === "import" && !userListLoaded) {
      loadUserList();
      setActiveCollapseKeys(['userList']); // Auto expand collapse
    }
  }, [groupType, isCreating, userListLoaded]);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // Load package types và promotion codes
      await Promise.all([
        loadPackageTypes(),
        loadPromotionCodes()
      ]);

      if (id && id !== "create") {
        await getGroupData();
      } else {
        // Đặt giá trị mặc định cho nhóm mới
        form.setFieldsValue({
          type: "automatic",
          automaticType: "never_used_after_signup"
        });
        setGroupType("automatic");
      }
    } catch (error) {
      console.error("Error loading initial data:", error);
      message.error(t("ERROR_LOADING_DATA"));
    } finally {
      setLoading(false);
    }
  };

  const loadPackageTypes = async () => {
    try {
      const packages = await getAllPackage();
      if (packages && Array.isArray(packages)) {
        const packageOptions = packages.map(pkg => ({
          value: pkg._id,
          label: pkg.name
        }));
        setPackageTypes(packageOptions);
      }
    } catch (error) {
      console.error("Error loading package types:", error);
    }
  };

  const loadPromotionCodes = async () => {
    try {
      const promotions = await getAllPromotions();
      if (promotions && Array.isArray(promotions)) {
        const promotionOptions = promotions.map(promo => ({
          value: promo._id,
          label: promo.displayText
        }));
        setPromotionCodes(promotionOptions);
      } else {
        console.log("No promotions found or invalid response");
        setPromotionCodes([]);
      }
    } catch (error) {
      console.error("Error loading promotion codes:", error);
      setPromotionCodes([]);
    }
  };

  const getGroupData = async () => {
    setLoading(true);
    try {
      const data = await getGroupDetail(id);
      if (data) {
        // Chuẩn bị dữ liệu cho form theo schema mới
        const formData = {
          ...data,
          registrationDateRange: data.conditions?.registrationDateRange ?
            [data.conditions.registrationDateRange.from ? dayjs(data.conditions.registrationDateRange.from) : null,
              data.conditions.registrationDateRange.to ? dayjs(data.conditions.registrationDateRange.to) : null] : null,
          paidRegistrationDateRange: data.conditions?.paidRegistrationDateRange ?
            [data.conditions.paidRegistrationDateRange.from ? dayjs(data.conditions.paidRegistrationDateRange.from) : null,
              data.conditions.paidRegistrationDateRange.to ? dayjs(data.conditions.paidRegistrationDateRange.to) : null] : null,
          packageExpiryDateRange: data.conditions?.packageExpiryDateRange ?
            [data.conditions.packageExpiryDateRange.from ? dayjs(data.conditions.packageExpiryDateRange.from) : null,
              data.conditions.packageExpiryDateRange.to ? dayjs(data.conditions.packageExpiryDateRange.to) : null] : null,
          feedbackStatus: data.conditions?.feedbackStatus,
          totalUsageDaysMin: data.conditions?.totalUsageDays?.min,
          totalUsageDaysMax: data.conditions?.totalUsageDays?.max,
          daysNotLoggedInMin: data.conditions?.daysNotLoggedIn?.min,
          daysNotLoggedInMax: data.conditions?.daysNotLoggedIn?.max,
          packageType: data.conditions?.packageType,
          usedPromotionCode: data.conditions?.usedPromotionCode
        };

        form.setFieldsValue(formData);
        setGroupType(data.type);
      } else {
        message.error(t("FAILED_TO_LOAD_GROUP_DATA"));
      }
    } catch (error) {
      console.error('Error loading group data:', error);
      message.error(t("ERROR_LOADING_GROUP_DATA"));
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      // Validate form trước khi submit
      const values = await form.validateFields();
      setLoading(true);

      let res;

      // Chuẩn bị dữ liệu để lưu theo schema mới
      const saveData = {
        ...values,
        conditions: {
          registrationDateRange: values.registrationDateRange ? {
            from: values.registrationDateRange[0]?.format('YYYY-MM-DD'),
            to: values.registrationDateRange[1]?.format('YYYY-MM-DD')
          } : null,
          paidRegistrationDateRange: values.paidRegistrationDateRange ? {
            from: values.paidRegistrationDateRange[0]?.format('YYYY-MM-DD'),
            to: values.paidRegistrationDateRange[1]?.format('YYYY-MM-DD')
          } : null,
          packageExpiryDateRange: values.packageExpiryDateRange ? {
            from: values.packageExpiryDateRange[0]?.format('YYYY-MM-DD'),
            to: values.packageExpiryDateRange[1]?.format('YYYY-MM-DD')
          } : null,
          feedbackStatus: values.feedbackStatus,
          totalUsageDays: (values.totalUsageDaysMin !== null && values.totalUsageDaysMin !== undefined) ||
          (values.totalUsageDaysMax !== null && values.totalUsageDaysMax !== undefined) ? {
            min: values.totalUsageDaysMin,
            max: values.totalUsageDaysMax
          } : null,
          daysNotLoggedIn: (values.daysNotLoggedInMin !== null && values.daysNotLoggedInMin !== undefined) ||
          (values.daysNotLoggedInMax !== null && values.daysNotLoggedInMax !== undefined) ? {
            min: values.daysNotLoggedInMin,
            max: values.daysNotLoggedInMax
          } : null,
          packageType: values.packageType,
          usedPromotionCode: values.usedPromotionCode
        }
      };

      // Xóa các trường không cần thiết
      delete saveData.registrationDateRange;
      delete saveData.paidRegistrationDateRange;
      delete saveData.packageExpiryDateRange;
      delete saveData.feedbackStatus;
      delete saveData.totalUsageDaysMin;
      delete saveData.totalUsageDaysMax;
      delete saveData.daysNotLoggedInMin;
      delete saveData.daysNotLoggedInMax;
      delete saveData.packageType;
      delete saveData.usedPromotionCode;

      if (id && id !== "create") {
        saveData._id = id;

        try {
          res = await updateGroup(saveData);
          if (res) {
            toast.success(t("UPDATE_GROUP_SUCCESS"));
            // Reload dữ liệu sau khi update thành công
            await getGroupData();
          } else {
            toast.error(t("UPDATE_GROUP_ERROR"));
          }
        } catch (updateError) {
          toast.error(t("UPDATE_GROUP_ERROR"));
        }
      } else {
        try {
          res = await createGroup(saveData);
          if (res) {
            toast.success(t("CREATE_GROUP_SUCCESS"));

            // Nếu có dữ liệu Excel preview và group type là import, upload dữ liệu
            if (excelPreviewData && saveData.type === "import" && excelUploadRef.current) {
              try {
                const uploadSuccess = await excelUploadRef.current.uploadPreviewedData(res._id || res.id);
                if (uploadSuccess) {
                  toast.success(t("EXCEL_DATA_UPLOADED_SUCCESS"));
                }
              } catch (uploadError) {
                console.error("Excel upload error:", uploadError);
                toast.error(t("EXCEL_UPLOAD_ERROR_AFTER_GROUP_CREATION"));
              }
            }

            // Chuyển đến trang edit với ID mới được tạo
            navigate(LINK.ADMIN.EMAIL_GROUP_ID.format(res._id || res.id));
          } else {
            toast.error(t("CREATE_GROUP_ERROR"));
          }
        } catch (createError) {
          toast.error(t("CREATE_GROUP_ERROR"));
        }
      }
    } catch (error) {
      if (error.errorFields) {
        // Form validation error
        toast.error(t("FORM_VALIDATION_ERROR"));
      } else {
        // API error
        toast.error(t("ERROR_SAVING_GROUP"));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGroupTypeChange = (e) => {
    const newType = e.target.value;
    setGroupType(newType);

    // Reset các field không cần thiết khi chuyển đổi loại nhóm
    if (newType === "automatic") {
      // Reset manual fields
      form.setFieldsValue({
        registrationDateRange: null,
        paidRegistrationDateRange: null,
        packageExpiryDateRange: null,
        feedbackStatus: null,
        totalUsageDaysMin: null,
        totalUsageDaysMax: null,
        daysNotLoggedInMin: null,
        daysNotLoggedInMax: null,
        packageType: null,
        usedPromotionCode: null
      });
    } else if (newType === "manual") {
      // Reset automatic fields
      form.setFieldsValue({
        automaticType: null
      });
    } else if (newType === "import") {
      // Reset both automatic and manual fields
      form.setFieldsValue({
        automaticType: null,
        registrationDateRange: null,
        paidRegistrationDateRange: null,
        packageExpiryDateRange: null,
        feedbackStatus: null,
        totalUsageDaysMin: null,
        totalUsageDaysMax: null,
        daysNotLoggedInMin: null,
        daysNotLoggedInMax: null,
        packageType: null,
        usedPromotionCode: null
      });
    }

    // Reset user list when changing group type
    setUserListData([]);
    setUserListTotal(0);
    setUserListLoaded(false);
    setActiveCollapseKeys([]);
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.EMAIL_GROUP);
  };

  const handleUploadSuccess = (result) => {
    // Reload user list after successful upload
    setUserListData([]);
    setUserListTotal(0);
    setUserListLoaded(false);

    // Auto-expand the user list to show imported users
    setActiveCollapseKeys(['userList']);

    // Show success message with details
    if (result.total) {
      toast.success(t("UPLOAD_SUCCESS_WITH_DETAILS", {
        total: result.total,
        success: result.success,
        failed: result.failed,
        duplicate: result.duplicate
      }));
    }

    // Force reload user list if collapse is already open
    if (activeCollapseKeys.includes('userList')) {
      setTimeout(() => {
        loadUserList();
      }, 500); // Small delay to ensure upload is processed
    }
  };

  const handlePreviewData = (previewData) => {
    setExcelPreviewData(previewData);
  };

  const createExcelFile = (data) => {
    // Create a minimal Excel XML structure
    const xmlContent = `<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Author>Email Marketing System</Author>
  <Created>${new Date().toISOString()}</Created>
 </DocumentProperties>
 <Styles>
  <Style ss:ID="Header">
   <Font ss:Bold="1"/>
   <Interior ss:Color="#F0F0F0" ss:Pattern="Solid"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Template">
  <Table>
   ${data.map((row, rowIndex) =>
     `<Row>
      ${row.map(cell =>
        `<Cell${rowIndex === 0 ? ' ss:StyleID="Header"' : ''}><Data ss:Type="String">${cell}</Data></Cell>`
      ).join('')}
     </Row>`
   ).join('')}
  </Table>
 </Worksheet>
</Workbook>`;

    return xmlContent;
  };

  const handleDownloadTemplate = () => {
    try {
      // Create Excel template data
      const templateData = [
        ['email', 'name', 'phone'],
        ['<EMAIL>', 'John Doe', '+1234567890'],
        ['<EMAIL>', 'Jane Smith', '+0987654321'],
        ['<EMAIL>', 'User Name', '+1122334455']
      ];

      // Option 1: Create proper Excel file
      const excelContent = createExcelFile(templateData);
      const blob = new Blob([excelContent], {
        type: 'application/vnd.ms-excel'
      });
      const filename = 'marketing_users_template.xls';

      // Option 2: Create CSV file (fallback)
      // const BOM = '\uFEFF';
      // const csvContent = BOM + templateData.map(row =>
      //   row.map(cell => {
      //     if (cell.includes(',') || cell.includes('"') || cell.includes('\n')) {
      //       return `"${cell.replace(/"/g, '""')}"`;
      //     }
      //     return cell;
      //   }).join(',')
      // ).join('\n');
      // const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      // const filename = 'marketing_users_template.csv';

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.click();
      window.URL.revokeObjectURL(url);

      toast.success(t("TEMPLATE_DOWNLOADED_SUCCESS"));
    } catch (error) {
      console.error('Error downloading template:', error);
      toast.error(t("TEMPLATE_DOWNLOAD_ERROR"));
    }
  };

  // Handle collapse change
  const handleCollapseChange = async (keys) => {
    setActiveCollapseKeys(keys);
    console.log("keys", keys)
    // Load data when collapse is opened and data not loaded yet
    if (keys.includes('userList') && !userListLoaded) {
      await loadUserList();
    }
  };

  const loadUserList = async () => {
    setLoadingUserList(true);
    setUserListError(null);

    try {
      if (groupType === "automatic") {
        const automaticType = form.getFieldValue("automaticType");
        const data = await getUsersByAutomaticType({automaticType});
        if (data && Array.isArray(data)) {
          setUserListData(data);
          setUserListTotal(data.length);
          setUserListLoaded(true);
        } else {
          setUserListData([]);
          setUserListTotal(0);
        }
      } else if (groupType === "manual") {
        // Lấy dữ liệu theo conditions cho manual group
        const formValues = form.getFieldsValue();
        const conditions = {
          registrationDateRange: formValues.registrationDateRange ? {
            from: formValues.registrationDateRange[0]?.format('YYYY-MM-DD'),
            to: formValues.registrationDateRange[1]?.format('YYYY-MM-DD')
          } : null,
          paidRegistrationDateRange: formValues.paidRegistrationDateRange ? {
            from: formValues.paidRegistrationDateRange[0]?.format('YYYY-MM-DD'),
            to: formValues.paidRegistrationDateRange[1]?.format('YYYY-MM-DD')
          } : null,
          packageExpiryDateRange: formValues.packageExpiryDateRange ? {
            from: formValues.packageExpiryDateRange[0]?.format('YYYY-MM-DD'),
            to: formValues.packageExpiryDateRange[1]?.format('YYYY-MM-DD')
          } : null,
          feedbackStatus: formValues.feedbackStatus,
          totalUsageDays: (formValues.totalUsageDaysMin !== null && formValues.totalUsageDaysMin !== undefined) ||
          (formValues.totalUsageDaysMax !== null && formValues.totalUsageDaysMax !== undefined) ? {
            min: formValues.totalUsageDaysMin,
            max: formValues.totalUsageDaysMax
          } : null,
          daysNotLoggedIn: (formValues.daysNotLoggedInMin !== null && formValues.daysNotLoggedInMin !== undefined) ||
          (formValues.daysNotLoggedInMax !== null && formValues.daysNotLoggedInMax !== undefined) ? {
            min: formValues.daysNotLoggedInMin,
            max: formValues.daysNotLoggedInMax
          } : null,
          packageType: formValues.packageType,
          usedPromotionCode: formValues.usedPromotionCode
        };

        // Gọi API với conditions (cần implement API endpoint mới)
        const data = await getUsersByAutomaticType({conditions});
        if (data && Array.isArray(data)) {
          setUserListData(data);
          setUserListTotal(data.length);
          setUserListLoaded(true);
        } else {
          setUserListData([]);
          setUserListTotal(0);
        }
      } else if (groupType === "import") {
        // For import type, get marketing users by group ID
        console.log("Loading marketing users for group:", id);
        const data = await getMarketingUsersByGroupId(id);
        console.log("Marketing users API response:", data);

        if (data && data.docs && Array.isArray(data.docs)) {
          // Transform data to match expected format
          const transformedData = data.docs.map(user => ({
            email: user.email,
            fullName: user.name || user.fullName,
            phone: user.phone || user.phoneNumber,
            _id: user._id
          }));
          console.log("Transformed marketing users data:", transformedData);
          setUserListData(transformedData);
          setUserListTotal(data.totalDocs || data.docs.length);
          setUserListLoaded(true);
        } else if (data && Array.isArray(data)) {
          // Handle case where API returns array directly
          const transformedData = data.map(user => ({
            email: user.email,
            fullName: user.name || user.fullName,
            phone: user.phone || user.phoneNumber,
            _id: user._id
          }));
          console.log("Transformed marketing users data (direct array):", transformedData);
          setUserListData(transformedData);
          setUserListTotal(data.length);
          setUserListLoaded(true);
        } else {
          console.log("No marketing users found or invalid response format");
          setUserListData([]);
          setUserListTotal(0);
          setUserListLoaded(true);
        }
      }
    } catch (error) {
      console.error('Error loading user list:', error);
      setUserListError(t("ERROR_LOADING_USER_LIST"));
      message.error(t("ERROR_LOADING_USER_LIST"));
    } finally {
      setLoadingUserList(false);
    }
  };

  const userListColumns = [
    {
      title: t("ORDER"), align: CONSTANT.CENTER,
      render: (_, __, index) => index + 1,
      width: 80,
    },
    {
      title: t("EMAIL"),
      dataIndex: "email",
      key: "email",
      width: groupType === "import" ? 250 : 300,
      render: (text) => <span className="name-value">{text}</span>, // Sử dụng class name-value giống trang danh sách
    },
    {
      title: t("FULL_NAME"),
      dataIndex: "fullName",
      key: "fullName",
      width: groupType === "import" ? 200 : 250,
      render: (text) => <span className="name-value">{text || "-"}</span>, // Sử dụng class name-value giống trang danh sách
    },
    // Add phone column for import type
    ...(groupType === "import" ? [{
      title: t("PHONE"),
      dataIndex: "phone",
      key: "phone",
      width: 150,
      render: (text) => <span className="name-value">{text || "-"}</span>,
    }] : []),
  ];

  const isCreating = !id || id === "create";

  return (
    <Loading active={isLoading} transparent>
      <div className="email-group-detail-container">
        <div className="email-group-detail__header">
          <div className="header-content">
            <div className="header-title">{isCreating ? t("CREATE_NEW_GROUP") : t("EDIT_GROUP")}</div>
            <div className="header-description">{t("EMAIL_GROUP_MANAGEMENT_DESCRIPTION")}</div>
          </div>
        </div>

        <div className="group-info-container">
          <div className="info-header">
            <h3 className="section-title">{t("GROUP_INFORMATION")}</h3>
          </div>
          <div className="info-content">
            <AntForm
              form={form}
              layout="vertical"
              requiredMark={true}
              className="form-group-info"
            >
              <div className="form-row">
                <AntForm.Item
                  name="name"
                  label={t("GROUP_NAME")}
                  rules={[RULE.REQUIRED]}
                  className="title-field"
                >
                  <Input placeholder={t("ENTER_GROUP_NAME")}/>
                </AntForm.Item>
              </div>

              <div className="form-row">
                <AntForm.Item
                  name="description"
                  label={t("DESCRIPTION")}
                  className="title-field"
                >
                  <Input.TextArea autoSize={{minRows: 2, maxRows: 4}} placeholder={t("ENTER_DESCRIPTION")}/>
                </AntForm.Item>
              </div>

              <div className="form-row">
                <AntForm.Item
                  name="type"
                  label={t("GROUP_TYPE")}
                  rules={[RULE.REQUIRED]}
                  className="type-field"
                >
                  <Radio.Group
                    options={GROUP_TYPES}
                    onChange={handleGroupTypeChange}
                  />
                </AntForm.Item>

                {groupType === "automatic" && (
                  <AntForm.Item
                    name="automaticType"
                    label={t("AUTOMATIC_TYPE")}
                    rules={[RULE.REQUIRED]}
                    className="type-field"
                  >
                    <Select
                      onChange={() => {
                        setUserListData([]);
                        setActiveCollapseKeys([]);
                        setUserListLoaded(false);
                      }}
                      placeholder={t("SELECT_AUTOMATIC_TYPE")}
                      options={AUTOMATIC_TYPES}
                    />
                  </AntForm.Item>
                )}
              </div>
            </AntForm>
          </div>
        </div>


        {groupType === "manual" && (
          <div className="group-conditions-container">
            <div className="info-header">
              <h3 className="section-title">{t("FILTER_CONDITIONS")}</h3>
            </div>
            <div className="info-content">
              <AntForm
                form={form}
                layout="vertical"
                requiredMark={false}
                className="form-group-conditions"
              >
                <div className="form-row">
                  <AntForm.Item
                    name="registrationDateRange"
                    label={t("REGISTRATION_DATE_RANGE")}
                  >
                    <DatePicker.RangePicker
                      style={{width: '100%'}}
                      format="DD/MM/YYYY"
                      onChange={() => {
                        setUserListData([]);
                        setActiveCollapseKeys([]);
                        setUserListLoaded(false);
                      }}
                    />
                  </AntForm.Item>

                  <AntForm.Item
                    name="paidRegistrationDateRange"
                    label={t("PAID_REGISTRATION_DATE_RANGE")}
                  >
                    <DatePicker.RangePicker
                      style={{width: '100%'}}
                      format="DD/MM/YYYY"
                      onChange={() => {
                        setUserListData([]);
                        setActiveCollapseKeys([]);
                        setUserListLoaded(false);
                      }}
                    />
                  </AntForm.Item>
                </div>

                <div className="form-row">
                  <AntForm.Item
                    name="packageExpiryDateRange"
                    label={t("PACKAGE_EXPIRY_DATE_RANGE")}
                  >
                    <DatePicker.RangePicker
                      style={{width: '100%'}}
                      format="DD/MM/YYYY"
                      onChange={() => {
                        setUserListData([]);
                        setActiveCollapseKeys([]);
                        setUserListLoaded(false);
                      }}
                    />
                  </AntForm.Item>

                  <AntForm.Item
                    name="feedbackStatus"
                    label={t("FEEDBACK_STATUS")}
                  >
                    <Select
                      placeholder={t("SELECT_FEEDBACK_STATUS")}
                      options={RESPONSE_STATUS}
                      allowClear
                      onChange={() => {
                        setUserListData([]);
                        setActiveCollapseKeys([]);
                        setUserListLoaded(false);
                      }}
                    />
                  </AntForm.Item>
                </div>

                {/*<div className="form-row">*/}
                {/*  <AntForm.Item*/}
                {/*    name="totalUsageDaysMin"*/}
                {/*    label={t("TOTAL_USAGE_DAYS_MIN")}*/}
                {/*  >*/}
                {/*    <InputNumber*/}
                {/*      placeholder={t("MIN_DAYS")}*/}
                {/*      min={0}*/}
                {/*      style={{width: '100%'}}*/}
                {/*      onChange={() => {*/}
                {/*        setUserListData([]);*/}
                {/*        setActiveCollapseKeys([]);*/}
                {/*        setUserListLoaded(false);*/}
                {/*      }}*/}
                {/*    />*/}
                {/*  </AntForm.Item>*/}

                {/*  <AntForm.Item*/}
                {/*    name="totalUsageDaysMax"*/}
                {/*    label={t("TOTAL_USAGE_DAYS_MAX")}*/}
                {/*  >*/}
                {/*    <InputNumber*/}
                {/*      placeholder={t("MAX_DAYS")}*/}
                {/*      min={0}*/}
                {/*      style={{width: '100%'}}*/}
                {/*      onChange={() => {*/}
                {/*        setUserListData([]);*/}
                {/*        setActiveCollapseKeys([]);*/}
                {/*        setUserListLoaded(false);*/}
                {/*      }}*/}
                {/*    />*/}
                {/*  </AntForm.Item>*/}
                {/*</div>*/}

                {/*<div className="form-row">*/}
                {/*  <AntForm.Item*/}
                {/*    name="daysNotLoggedInMin"*/}
                {/*    label={t("DAYS_NOT_LOGGED_IN_MIN")}*/}
                {/*  >*/}
                {/*    <InputNumber*/}
                {/*      placeholder={t("MIN_DAYS")}*/}
                {/*      min={0}*/}
                {/*      style={{width: '100%'}}*/}
                {/*      onChange={() => {*/}
                {/*        setUserListData([]);*/}
                {/*        setActiveCollapseKeys([]);*/}
                {/*        setUserListLoaded(false);*/}
                {/*      }}*/}
                {/*    />*/}
                {/*  </AntForm.Item>*/}

                {/*  <AntForm.Item*/}
                {/*    name="daysNotLoggedInMax"*/}
                {/*    label={t("DAYS_NOT_LOGGED_IN_MAX")}*/}
                {/*  >*/}
                {/*    <InputNumber*/}
                {/*      placeholder={t("MAX_DAYS")}*/}
                {/*      min={0}*/}
                {/*      style={{width: '100%'}}*/}
                {/*      onChange={() => {*/}
                {/*        setUserListData([]);*/}
                {/*        setActiveCollapseKeys([]);*/}
                {/*        setUserListLoaded(false);*/}
                {/*      }}*/}
                {/*    />*/}
                {/*  </AntForm.Item>*/}
                {/*</div>*/}

                <div className="form-row">
                  <AntForm.Item
                    name="packageType"
                    label={t("PACKAGE_TYPE")}
                  >
                    <Select
                      placeholder={t("SELECT_PACKAGE_TYPE")}
                      options={packageTypes}
                      allowClear
                      onChange={() => {
                        setUserListData([]);
                        setActiveCollapseKeys([]);
                        setUserListLoaded(false);
                      }}
                    />
                  </AntForm.Item>

                  {/*<AntForm.Item*/}
                  {/*  name="usedPromotionCode"*/}
                  {/*  label={t("USED_PROMOTION_CODE")}*/}
                  {/*>*/}
                  {/*  <Select*/}
                  {/*    placeholder={t("SELECT_PROMOTION_CODE")}*/}
                  {/*    options={promotionCodes}*/}
                  {/*    allowClear*/}
                  {/*    onChange={() => {*/}
                  {/*      setUserListData([]);*/}
                  {/*      setActiveCollapseKeys([]);*/}
                  {/*      setUserListLoaded(false);*/}
                  {/*    }}*/}
                  {/*  />*/}
                  {/*</AntForm.Item>*/}
                </div>
              </AntForm>
            </div>
          </div>
        )}

        {/* Import Excel Section - Only show when group exists and type is import */}
        {groupType === "import" && !isCreating && (
          <div className="group-import-container">
            <div className="info-header">
              <h3 className="section-title">{t("IMPORT_EXCEL_FILE")}</h3>
              <AntButton
                type={BUTTON.PRIMARY}
                icon={<DownloadOutlined />}
                onClick={handleDownloadTemplate}
                size="middle"
                className="download-template-btn"
              >
                {t("DOWNLOAD_TEMPLATE")}
              </AntButton>
            </div>
            <div className="info-content">
              <ExcelUpload
                groupId={id}
                onUploadSuccess={handleUploadSuccess}
                onPreviewData={handlePreviewData}
                hasExistingUsers={userListData.length > 0}
                hideDownloadButton={true}
              />
            </div>
          </div>
        )}

        {/* Excel Upload for creating import group */}
        {groupType === "import" && isCreating && (
          <div className="group-import-container">
            <div className="info-header">
              <h3 className="section-title">{t("IMPORT_EXCEL_FILE")}</h3>
              <AntButton
                type={BUTTON.PRIMARY}
                icon={<DownloadOutlined />}
                onClick={handleDownloadTemplate}
                size="middle"
                className="download-template-btn"
              >
                {t("DOWNLOAD_TEMPLATE")}
              </AntButton>
            </div>
            <div className="info-content">
              <Alert
                type="info"
                showIcon
                message={t("PREVIEW_EXCEL_BEFORE_SAVE")}
                description={t("PREVIEW_EXCEL_BEFORE_SAVE_DESCRIPTION")}
                style={{ marginBottom: 16 }}
              />
              <ExcelUpload
                ref={excelUploadRef}
                groupId={null}
                onPreviewData={handlePreviewData}
                allowPreviewOnly={true}
                hasExistingUsers={false}
                hideDownloadButton={true}
              />
            </div>
          </div>
        )}

        {/* User List Section - Sử dụng Collapse để hiển thị danh sách user theo AutomaticType hoặc Conditions */}
        {!isCreating && (
          <Card className="email-marketing-table-card">
            <Collapse
              activeKey={activeCollapseKeys}
              onChange={handleCollapseChange}
              className="user-list-collapse"
              items={[
                {
                  key: 'userList',
                  label: (
                    <div className="collapse-header">
                      <UserOutlined style={{marginRight: 8}}/>
                      <span>
                        {groupType === "automatic"
                          ? t("USER_LIST_BY_AUTOMATIC_TYPE")
                          : groupType === "manual"
                            ? t("USER_LIST_BY_CONDITIONS")
                            : t("IMPORTED_USERS_LIST")
                        }
                      </span>
                      {(userListData.length > 0 || userListTotal > 0) && (
                        <span className="user-count-badge">
                          ({groupType === "import" && userListTotal > userListData.length
                            ? `${userListData.length}/${userListTotal}`
                            : userListData.length})
                        </span>
                      )}
                    </div>
                  ),
                  children: (
                    <div className="user-list-content">
                      {userListError ? (
                        <div className="error-message">
                          <p style={{color: '#ff4d4f', textAlign: 'center', padding: '20px'}}>
                            {userListError}
                          </p>
                        </div>
                      ) : (
                        <TableAdmin
                          columns={userListColumns}
                          dataSource={userListData}
                          loading={loadingUserList}
                          rowKey="email"
                          pagination={true}
                          scroll={{x: 600}}
                          className="email-marketing-table"
                          rowClassName={() => "email-marketing-table-row"}
                          locale={{emptyText: t("NO_USERS_IN_GROUP")}}
                        />
                      )}
                    </div>
                  ),
                },
              ]}
            />
          </Card>
        )}

        <div className="save-button-container">
          <AntButton
            type={BUTTON.LIGHT_NAVY}
            size="large"
            onClick={handleCancel}
            style={{marginRight: '16px'}}
            className="cancel-button"
          >
            {t("CANCEL")}
          </AntButton>
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            onClick={handleSave}
            className="save-button"
            icon={<SaveOutlined/>}
          >
            {isCreating ? t("CREATE") : t("SAVE")}
          </AntButton>
        </div>
      </div>
    </Loading>
  );
};

export default EmailGroupDetail;
