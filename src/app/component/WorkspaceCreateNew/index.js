import { Dropdown } from "antd";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { connect } from "react-redux";

import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";

import PlusIcon from "@component/SvgIcons/PlusIcon";

import { createFolder } from "@services/Folder";
import { createProject } from "@services/Project";

import { BUTTON, CONSTANT } from "@constant";
import { LINK } from "@link";
import "./WorkspaceCreateNew.scss";

import * as workspaceRedux from "@src/ducks/workspace.duck";
import ModalCreateNew from "@component/CreateNewModal";

function WorkspaceCreateNew({ availableWorkspaces, ...props }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { workspaceId } = props;

  const [modalCreateState, setModalCreateState] = useState({
    open: false,
    createType: null
  });

  async function onCreateFolder(data) {
    const apiRequest = { folderName: data.folderName, workspaceId: workspaceId };
    const apiResponse = await createFolder(apiRequest);
    if (apiResponse) {
      const newWorkspaces = availableWorkspaces.map(workspace => {
        return workspace?._id === workspaceId ? { ...workspace, folders: workspace.folders + 1 } : workspace;
      })
      props.setAvailableWorkspaces(newWorkspaces);

      navigate(LINK.FOLDER_DETAIL.format(apiResponse._id));
      toast.success("CREATE_FOLDER_SUCCESS");
      toggleModalCreateNew(null);
    }
  }

  async function onCreateProject(data) {
    const projectParams = {
      ...data,
      workspaceId: workspaceId,
    };
    const apiResponse = await createProject(projectParams);
    if (apiResponse) {
      const newWorkspaces = availableWorkspaces.map(workspace => {
        return workspace?._id === workspaceId ? { ...workspace, projects: workspace.projects + 1 } : workspace;
      })
      props.setAvailableWorkspaces(newWorkspaces);
      navigate(LINK.PROJECT_DETAIL.format(apiResponse._id));
      const messageLang = modalCreateState.createType === CONSTANT.PROJECT_LESSON ? "CREATE_PROJECT_LESSON_SUCCESS" : "CREATE_PROJECT_EXAM_SUCCESS"
      toast.success(messageLang);
      toggleModalCreateNew(null);
    }
  }

  const toggleModalCreateNew = (type) => {
    setModalCreateState(pre => ({ open: !pre.open, createType: type }));
  };

  const onSubmit = async (values) => {
    if (modalCreateState.createType === CONSTANT.FOLDER) {
      await onCreateFolder(values);
    } else {
      await onCreateProject(values);
    }
  };

  const onCreateProjectExam = () => {
    navigate(LINK.CREATE_EXAM);
  }

  return (<>
    <Dropdown
      menu={{
        items: [
          { key: "1", label: t("CREATE_FOLDER"), onClick: () => toggleModalCreateNew(CONSTANT.FOLDER) },
          { key: "2", label: t("CREATE_PROJECT_LESSON"), onClick: () => toggleModalCreateNew(CONSTANT.PROJECT_LESSON) },
          { key: "3", label: t("CREATE_PROJECT_EXAM"), onClick: onCreateProjectExam },
          { key: "4", label: t("CREATE_PROJECT_GRADING"), onClick: () => toggleModalCreateNew(CONSTANT.PROJECT_GRADING_ASSIGNMENT) },
        ],
        className: "create-new-menu",
      }}
      trigger={["click"]}
      placement="bottomRight"
      className="create-new-dropdown"
    >
      <AntButton size="large" type={BUTTON.DEEP_GREEN} className="create-new-btn">
        {t("CREATE_NEW")}
        <PlusIcon />
      </AntButton>
    </Dropdown>

    <ModalCreateNew
      {...modalCreateState}
      onCancel={() => toggleModalCreateNew(null)}
      onSubmit={onSubmit} />
  </>
  );
}

function mapStateToProps(store) {
  const { availableWorkspaces } = store.workspace;
  return { availableWorkspaces };
}

const mapDispatchToProps = {
  ...workspaceRedux.actions
}

export default connect(mapStateToProps, mapDispatchToProps)(WorkspaceCreateNew);
