.owllee-chat {

  .toggle-owllee {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border-width: 2px;

    .toggle-owllee__icon {
      border-radius: 50%;
      transform: all 0.3s ease-in-out;

      &:not(.toggle-owllee__icon-close) {
        width: 56px;
        height: 56px;
      }

      &.toggle-owllee__icon-close {
        width: 24px;
        height: 24px;
      }

    }
  }

  .owllee-chat-iframe {
    position: fixed;
    z-index: 10;
    bottom: 100px;
    width: 450px;
    height: 600px;
    right: 20px;
    border-radius: 10px;
    border: 10x solid #ffffff00;
    box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
    transition: opacity 0.2s ease-in-out;
    user-select: none;
    max-height: 73vh;
    max-width: 90vw;
    -moz-user-select: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    display: block;
  }

  .owllee-chat-iframe__hide {
    display: none;
  }

  .icon-tabler-x {
    width: 80%;
    height: 80%;
  }
}

