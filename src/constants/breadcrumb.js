import { <PERSON>IN<PERSON> } from "@link";
import { CONSTANT } from "@constant";

import "@src/common/prototype";

const BREADCRUMB = [
  {
    path: LINK.WELCOME,
    items: [{ lang: "WELCOME", url: LINK.WELCOME }],
  },
  {
    path: LINK.HOMEPAGE,
    items: [{ lang: "HOMEPAGE", url: LINK.HOMEPAGE }],
  },
  {
    path: LINK.TOOLS,
    items: [{ lang: "TOOL", url: LINK.TOOLS }],
  },
  {
    path: LINK.STARRED,
    items: [{ lang: "MY_STARRED", url: LINK.STARRED }],
  },
  {
    path: LINK.MY_WORKSPACE,
    items: [{ lang: "MY_WORKSPACE", url: LINK.MY_WORKSPACE }],
  },
  {
    path: LINK.SHARE_WITH_ME,
    items: [{ lang: "SHARE_WITH_ME", url: LINK.SHARE_WITH_ME }],
  },
  {
    path: LINK.RESOURCE,
    items: [{ lang: "RESOURCE", url: LINK.RESOURCE }],
  },
  {
    path: LINK.ACCOUNT,
    items: [{ lang: "ACCOUNT_SETTINGS", url: LINK.ACCOUNT }],
  },
  {
    path: LINK.PAYMENT_HISTORY,
    items: [
      { lang: "ACCOUNT_SETTINGS", url: LINK.ACCOUNT_SETTINGS },
      { lang: "PAYMENT_HISTORY", url: LINK.PAYMENT_HISTORY },
    ],
  },
  {
    path: LINK.PROJECT_DETAIL.format(":id"),
    items: [
      { lang: "FOLDER", url: LINK.FOLDER_DETAIL, urlType: CONSTANT.FOLDER_ID },
      { lang: "PROJECT", url: LINK.PROJECT_DETAIL, urlType: CONSTANT.PROJECT_ID },
    ],
  },
  {
    path: LINK.FOLDER_DETAIL.format(":id"),
    items: [
      { lang: "FOLDER", url: LINK.FOLDER_DETAIL, urlType: CONSTANT.FOLDER_ID },
    ],
  },
  {
    path: LINK.TEMPLATE,
    items: [{ lang: "TEMPLATE_MENU_LABEL", url: LINK.TEMPLATE }],
  },
  {
    path: LINK.MY_DASHBOARD,
    items: [{ lang: "MY_DASHBOARD", url: LINK.MY_DASHBOARD }],
  },
  {
    path: LINK.ORG_DASHBOARD,
    items: [{ lang: "ORGANIZATION_DASHBOARD", url: LINK.ORG_DASHBOARD }],
  },
  {
    path: LINK.ORGANIZATION,
    items: [{ lang: "ORGANIZATION_SETTING", url: LINK.ORGANIZATION }],
  },
  {
    path: LINK.PACKAGE,
    items: [{ lang: "PACKAGE_SETTING", url: LINK.PACKAGE }],
  },
  {
    path: LINK.PACKAGE_CREATE,
    items: [{ lang: "CREATE_PACKAGE", url: LINK.PACKAGE_CREATE }],
  },
  {
    path: LINK.PACKAGE_ID.format(":id"),
    items: [{ lang: "PACKAGE_DETAIL", url: LINK.PACKAGE_ID }],
  },
  {
    path: LINK.ORGANIZATION_WORKSPACE,
    items: [{ lang: "WORKSPACE", url: LINK.ORGANIZATION_WORKSPACE, urlType: CONSTANT.WORKSPACE_NAME }],
  },
  {
    path: LINK.ADMIN_TOOL,
    items: [{ lang: "TOOLS", url: LINK.ADMIN_TOOL }],
  },
  {
    path: LINK.TOOL_DETAIL.format(":id"),
    items: [{ lang: "TOOLS", url: LINK.ADMIN_TOOL }, { lang: "TOOL_DETAIL", url: LINK.TOOL_DETAIL }],
  },
  {
    path: LINK.ADMIN_GROUP_TOOL,
    items: [{ lang: "GROUP_TOOL", url: LINK.ADMIN_GROUP_TOOL }],
  },
  {
    path: LINK.ADMIN_GROUP_TOOL_ID.format(":id"),
    items: [{ lang: "GROUP_TOOL", url: LINK.ADMIN_GROUP_TOOL }, {
      lang: "GROUP_TOOL_DETAIL",
      url: LINK.ADMIN_GROUP_TOOL_ID.format(":id"),
    }],
  },
  {
    path: LINK.ADMIN_PERSONA,
    items: [{ lang: "PERSONA", url: LINK.ADMIN_PERSONA }],
  },
  {
    path: LINK.ADMIN_PAGE + LINK.CREATE_PERSONA,
    items: [{ lang: "PERSONA", url: LINK.ADMIN_PAGE + LINK.ADMIN_PERSONA }, {
      lang: "CREATE_PERSONA",
      url: LINK.ADMIN_PAGE + LINK.CREATE_PERSONA,
    }],
  },
  {
    path: LINK.ADMIN_PAGE + LINK.PERSONA_DETAIL.format(":id"),
    items: [{ lang: "PERSONA", url: LINK.ADMIN_PAGE + LINK.ADMIN_PERSONA }, {
      lang: "PERSONA_DETAIL",
      url: LINK.ADMIN_PAGE + LINK.PERSONA_DETAIL.format(":id"),
    }],
  },
  {
    path: LINK.ADMIN_CUSTOMER,
    items: [{ lang: "CUSTOMER", url: LINK.ADMIN_CUSTOMER }],
  },
  {
    path: LINK.ADMIN_INSTRUCTION,
    items: [{ lang: "INSTRUCTION", url: LINK.ADMIN_INSTRUCTION }],
  },
  {
    path: LINK.INSTRUCTION_DETAIL.format(":id"),
    items: [{ lang: "INSTRUCTION", url: LINK.ADMIN_INSTRUCTION }, {
      lang: "INSTRUCTION_DETAIL",
      url: LINK.INSTRUCTION_DETAIL.format(":id"),
    }],
  },
  {
    path: LINK.INSTRUCTION_CREATE,
    items: [{ lang: "INSTRUCTION", url: LINK.ADMIN_INSTRUCTION }, {
      lang: "CREATE_INSTRUCTION",
      url: LINK.INSTRUCTION_CREATE,
    }],
  },
  {
    path: LINK.ADMIN_OPTIONS,
    items: [{ lang: "INSTRUCTION_OPTION", url: LINK.ADMIN_OPTIONS }],
  },
  {
    path: LINK.ADMIN_OPTIONS_CREATE,
    items: [
      { lang: "INSTRUCTION_OPTION", url: LINK.ADMIN_OPTIONS },
      { lang: "CREATE_INSTRUCTION_OPTION", url: LINK.ADMIN_OPTIONS_CREATE }
    ],
  },
  {
    path: LINK.ADMIN_OPTIONS_DETAIL.format(":id"),
    items: [
      { lang: "INSTRUCTION_OPTION", url: LINK.ADMIN_OPTIONS },
      { lang: "INSTRUCTION_OPTION_DETAIL", url: LINK.ADMIN_OPTIONS_DETAIL.format(":id"), }
    ],
  },
  {
    path: LINK.ADMIN_VOICE_OPTIONS,
    items: [{ lang: "VOICE_OPTION", url: LINK.ADMIN_VOICE_OPTIONS }],
  },
  {
    path: LINK.ADMIN_VOICE_OPTION_CREATE,
    items: [
      { lang: "VOICE_OPTION", url: LINK.ADMIN_VOICE_OPTIONS },
      { lang: "VOICE_OPTION_CREATE", url: LINK.ADMIN_VOICE_OPTION_CREATE }
    ],
  },
  {
    path: LINK.ADMIN_VOICE_OPTION_DETAIL.format(":id"),
    items: [
      { lang: "VOICE_OPTION", url: LINK.ADMIN_VOICE_OPTIONS },
      { lang: "VOICE_OPTION_DETAIL", url: LINK.ADMIN_VOICE_OPTION_DETAIL.format(":id"), }
    ],
  },
  {
    path: LINK.ADMIN_OUTPUT_TYPE,
    items: [{ lang: "OUTPUT_TYPE", url: LINK.ADMIN_OUTPUT_TYPE }],
  },
  {
    path: LINK.ADMIN_KNOWLEDGE,
    items: [{ lang: "KNOWLEDGE", url: LINK.ADMIN_KNOWLEDGE }],
  },
  {
    path: LINK.ADMIN_KNOWLEDGE_DETAIL.format(":id"),
    items: [{ lang: "KNOWLEDGE", url: LINK.ADMIN_KNOWLEDGE }, {
      lang: "KNOWLEDGE_DETAIL",
      url: LINK.ADMIN_KNOWLEDGE_DETAIL.format(":id"),
    }],
  },
  {
    path: LINK.KNOWLEDGE_CREATE,
    items: [{ lang: "KNOWLEDGE", url: LINK.ADMIN_KNOWLEDGE }, { lang: "CREATE_KNOWLEDGE", url: LINK.KNOWLEDGE_CREATE }],
  },
  {
    path: LINK.ADMIN_PAYMENT_HISTORY_ID.format(":id"),
    items: [{ lang: "CUSTOMER", url: LINK.ADMIN_CUSTOMER }, { lang: "PAYMENT_HISTORY" }],
  },
  {
    path: LINK.ADMIN_PAGE + LINK.USER_TRACKING,
    items: [{ lang: "USER_TRACKING", url: LINK.USER_TRACKING }],
  },
  {
    path: LINK.ADMIN_AVERAGE_TOKEN,
    items: [{ lang: "TOKEN_TOOL", url: LINK.ADMIN_AVERAGE_TOKEN }],
  },
  {
    path: LINK.ADMIN_TOKEN_INSTRUCTION,
    items: [{ lang: "TOKEN_INSTRUCTION", url: LINK.ADMIN_TOKEN_INSTRUCTION }],
  },
  {
    path: LINK.ADMIN.API_KEY,
    items: [{ lang: "API_KEY", url: LINK.ADMIN.API_KEY }],
  },
  {
    path: LINK.ADMIN_PAGE + LINK.OPENAI_COST,
    items: [{ lang: "OPENAI_COST", url: LINK.OPENAI_COST }],
  },
  {
    path: LINK.ADMIN.DOCUMENT_TEMPLATE,
    items: [{ lang: "DOCUMENT_TEMPLATE", url: LINK.ADMIN.DOCUMENT_TEMPLATE }],
  },
  {
    path: LINK.CREATE_EXAM,
    items: [{ lang: "CREATE_EXAM", url: LINK.CREATE_EXAM }],
  },
  {
    path: LINK.EXAM_TEMPLATE_FOLDER_ID.format(":id"),
    items: [
      { lang: "CREATE_EXAM", url: LINK.CREATE_EXAM },
      { url: LINK.EXAM_TEMPLATE_FOLDER_ID, urlType: CONSTANT.FOLDER_ID },
    ],
  },
  {
    path: LINK.ADMIN.DOCUMENT_TEMPLATE_ID.format(":id"),
    items: [
      { lang: "DOCUMENT_TEMPLATE", url: LINK.ADMIN.DOCUMENT_TEMPLATE },
      { lang: "DOCUMENT_TEMPLATE_DETAIL" },
    ],
  },
  {
    path: LINK.MARK_EXAM,
    items: [{ lang: "MARK_EXAM", url: LINK.MARK_EXAM }],
  },
  {
    path: LINK.ADMIN_PAGE + LINK.WAITING_LIST,
    items: [{ lang: "WAITING_LIST", url: LINK.WAITING_LIST }],
  },
  {
    path: LINK.ADMIN_PAGE + LINK.WHITELIST,
    items: [{ lang: "WHITELIST", url: LINK.WHITELIST }],
  },
  {
    path: LINK.ADMIN.ORGANIZATION,
    items: [
      { lang: "ORGANIZATION", url: LINK.ADMIN.ORGANIZATION },
    ],
  },
  {
    path: LINK.ADMIN.ORGANIZATION_ID.format(":id"),
    items: [
      { lang: "ORGANIZATION", url: LINK.ADMIN.ORGANIZATION },
      { lang: "ORGANIZATION_DETAIL" },
    ],
  },
  {
    path: LINK.ADMIN.DISCOUNT,
    items: [{ lang: "DISCOUNT", url: LINK.ADMIN.DISCOUNT }],
  },
  {
    path: LINK.ADMIN.PROMOTION,
    items: [{ lang: "PROMOTION", url: LINK.ADMIN.PROMOTION }],
  },
  {
    path: LINK.ADMIN.TRANSACTION_HISTORY,
    items: [{ lang: "TRANSACTION_HISTORY", url: LINK.ADMIN.TRANSACTION_HISTORY }],
  },
  {
    path: LINK.ADMIN.USER,
    items: [{ lang: "USER", url: LINK.ADMIN.USER }],
  },
  {
    path: LINK.ADMIN_FEATURES,
    items: [{ lang: "FEATURES_MANAGEMENT", url: LINK.ADMIN_FEATURES }],
  },
  {
    path: LINK.ADMIN_FEATURES_CREATE,
    items: [
      { lang: "FEATURES_MANAGEMENT", url: LINK.ADMIN_FEATURES },
      { lang: "CREATE_FEATURE" },
    ],
  },
  {
    path: LINK.ADMIN_FEATURES_DETAIL.format(":id"),
    items: [
      { lang: "FEATURES_MANAGEMENT", url: LINK.ADMIN_FEATURES },
      { lang: "FEATURE_DETAIL" },
    ],
  },
  {
    path: LINK.ADMIN.FEEDBACK_STATISTIC,
    items: [
      { lang: "FEEDBACK_STATISTIC", url: LINK.ADMIN.FEEDBACK_STATISTIC },
    ],
  },
  {
    path: LINK.ADMIN.FEEDBACK_ANALYSIS,
    items: [
      { lang: "FEEDBACK_ANALYSIS", url: LINK.ADMIN.FEEDBACK_ANALYSIS },
    ],
  },
  {
    path: LINK.ADMIN_EXPLAIN,
    items: [
      { lang: "EXPLAIN_INSTRUCTION", url: LINK.ADMIN_EXPLAIN },
    ],
  },
  {
    path: LINK.ADMIN_EXPLAIN_DETAIL.format(":id"),
    items: [
      { lang: "EXPLAIN_INSTRUCTION", url: LINK.ADMIN_EXPLAIN },
      { lang: "EDIT_EXPLAIN" },
    ],
  },

  {
    path: LINK.ADMIN.SETTING,
    items: [
      { lang: "SYSTEM_SETTINGS", url: LINK.ADMIN.SETTING },
    ],
  },
  {
    path: LINK.ADMIN.SUPPORT_BUSINESS,
    items: [
      { lang: "SUPPORT_BUSINESS", url: LINK.ADMIN.SUPPORT_BUSINESS },
    ],
  },
  {
    path: LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT,
    items: [{ lang: "DICTATION_SHADOWING_MANAGEMENT", url: LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT }],
  },
  {
    path: LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT_ID.format(":id"),
    items: [
      { lang: "DICTATION_SHADOWING_MANAGEMENT", url: LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT },
      { lang: "DICTATION_SHADOWING_DETAIL" },
    ],
  },
  {
    path: LINK.ADMIN.SPEAKING_EXERCISE,
    items: [{ lang: "SPEAKING_EXERCISE_MANAGEMENT", url: LINK.ADMIN.SPEAKING_EXERCISE }],
  },
  {
    path: LINK.ADMIN.SPEAKING_EXERCISE_ID.format(":id"),
    items: [
      { lang: "SPEAKING_EXERCISE_MANAGEMENT", url: LINK.ADMIN.SPEAKING_EXERCISE },
      { lang: "SPEAKING_EXERCISE_DETAIL" },
    ],
  },
  // Email Marketing
  {
    path: LINK.ADMIN.EMAIL_CAMPAIGN,
    items: [{ lang: "EMAIL_CAMPAIGN_MANAGEMENT", url: LINK.ADMIN.EMAIL_CAMPAIGN }],
  },
  {
    path: LINK.ADMIN.EMAIL_CAMPAIGN_CREATE,
    items: [
      { lang: "EMAIL_CAMPAIGN_MANAGEMENT", url: LINK.ADMIN.EMAIL_CAMPAIGN },
      { lang: "CREATE_CAMPAIGN" },
    ],
  },
  {
    path: LINK.ADMIN.EMAIL_CAMPAIGN_ID.format(":id"),
    items: [
      { lang: "EMAIL_CAMPAIGN_MANAGEMENT", url: LINK.ADMIN.EMAIL_CAMPAIGN },
      { lang: "EMAIL_CAMPAIGN_DETAIL" },
    ],
  },
  {
    path: LINK.ADMIN.EMAIL_GROUP,
    items: [{ lang: "EMAIL_GROUP_MANAGEMENT", url: LINK.ADMIN.EMAIL_GROUP }],
  },
  {
    path: LINK.ADMIN.EMAIL_GROUP_CREATE,
    items: [
      { lang: "EMAIL_GROUP_MANAGEMENT", url: LINK.ADMIN.EMAIL_GROUP },
      { lang: "CREATE_GROUP" },
    ],
  },
  {
    path: LINK.ADMIN.EMAIL_GROUP_ID.format(":id"),
    items: [
      { lang: "EMAIL_GROUP_MANAGEMENT", url: LINK.ADMIN.EMAIL_GROUP },
      { lang: "EMAIL_GROUP_DETAIL" },
    ],
  },
  {
    path: LINK.ADMIN.EMAIL_TEMPLATE,
    items: [{ lang: "EMAIL_TEMPLATE_MANAGEMENT", url: LINK.ADMIN.EMAIL_TEMPLATE }],
  },
  {
    path: LINK.ADMIN.EMAIL_TEMPLATE_CREATE,
    items: [
      { lang: "EMAIL_TEMPLATE_MANAGEMENT", url: LINK.ADMIN.EMAIL_TEMPLATE },
      { lang: "CREATE_TEMPLATE" },
    ],
  },
  {
    path: LINK.ADMIN.EMAIL_TEMPLATE_ID.format(":id"),
    items: [
      { lang: "EMAIL_TEMPLATE_MANAGEMENT", url: LINK.ADMIN.EMAIL_TEMPLATE },
      { lang: "EMAIL_TEMPLATE_DETAIL" },
    ],
  },
  {
    path: LINK.ADMIN.EMAIL_STATISTICS,
    items: [{ lang: "EMAIL_STATISTICS", url: LINK.ADMIN.EMAIL_STATISTICS }],
  },
  {
    path: LINK.ADMIN.ERROR_REPORTS,
    items: [{ lang: "ERROR_REPORTS", url: LINK.ADMIN.ERROR_REPORTS }],
  },
];

export default BREADCRUMB;
