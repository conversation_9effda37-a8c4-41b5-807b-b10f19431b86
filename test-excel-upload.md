# Test Excel Upload Flow

## C<PERSON><PERSON> thay đổi đã thực hiện:

### 1. ExcelUpload Component
- Thêm props: `onPreviewData`, `allowPreviewOnly`
- Thêm state: `previewData`, `isPreviewMode`
- <PERSON><PERSON><PERSON> riêng logic preview và upload
- <PERSON><PERSON> dụng forwardRef để expose `uploadPreviewedData` function
- Thêm UI hiển thị preview data

### 2. EmailGroupDetail Component
- Thêm state: `excelPreviewData`, `excelUploadRef`
- Cập nhật logic handleSave để upload Excel data sau khi tạo group
- Thêm callback `handlePreviewData`
- Hi<PERSON>n thị ExcelUpload với preview mode khi tạo group mới

### 3. Translation Keys
- Thêm các key mới cho preview functionality

### 4. CSS
- Thêm styles cho preview data section

## Luồng hoạt động mới:

### Khi tạo group mới (type = import):
1. <PERSON><PERSON><PERSON><PERSON> dùng chọn file Excel
2. File được đọc và hiển thị preview (mock data)
3. Người dùng điền thông tin group và bấm "Tạo mới"
4. Hệ thống tạo group trước
5. Sau khi tạo group thành công, upload dữ liệu Excel với mktGroupId vừa tạo
6. Chuyển đến trang edit group

### Khi edit group hiện có (type = import):
- Giữ nguyên logic cũ: upload trực tiếp

## Test Cases:

1. **Tạo group mới với type import:**
   - Chọn file Excel → hiển thị preview
   - Điền thông tin group → bấm "Tạo mới"
   - Kiểm tra group được tạo và dữ liệu được upload

2. **Edit group hiện có:**
   - Upload file Excel → upload trực tiếp như cũ

3. **Validation:**
   - File không đúng định dạng
   - File quá lớn
   - Group đã có dữ liệu

## Cần test thêm:
- Error handling khi upload thất bại
- Cancel upload
- UI responsive
- Dark theme support
